import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';
import 'dart:async';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// A configurable location widget that can display and capture location information.
///
/// This widget provides a rich set of customization options for displaying
/// and capturing location data, including map display, address lookup,
/// current location detection, and various styling options.
class LocationWidget extends StatefulWidget {
  // Display mode
  final LocationDisplayMode displayMode;

  // Map properties
  final double? initialLatitude;
  final double? initialLongitude;
  final double mapZoom;
  final bool showMapControls;
  final MapType mapType;
  final bool enableMapGestures;
  final Set<Marker>? markers;
  final bool showUserLocation;

  // Address properties
  final String? initialAddress;
  final bool showAddressField;
  final bool enableAddressSearch;
  final bool showCoordinates;
  final String? addressHint;
  final bool readOnly;

  // Current location properties
  final bool showCurrentLocationButton;
  final bool autoDetectLocation;
  final LocationAccuracy locationAccuracy;

  // Appearance properties
  final double? width;
  final double? height;
  final Color backgroundColor;
  final double borderRadius;
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;
  final bool hasShadow;
  final double elevation;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Label properties
  final String? label;
  final TextStyle? labelStyle;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final bool showLabel;

  // Button properties
  final String? currentLocationButtonText;
  final String? searchButtonText;
  final Color buttonColor;
  final Color buttonTextColor;

  // Callback functions
  final Function(double latitude, double longitude)? onLocationChanged;
  final Function(String address)? onAddressChanged;
  final Function(Position position)? onCurrentLocationDetected;
  final Function(String error)? onError;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Animation properties
  /// Whether to animate the widget when it changes
  final bool hasAnimation;

  /// Duration of the animation
  final Duration animationDuration;

  /// Curve to use for the animation
  final Curve animationCurve;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Location-specific JSON configuration
  /// Whether to use JSON location configuration
  final bool useJsonLocationConfig;

  /// Location-specific JSON configuration
  final Map<String, dynamic>? locationConfig;

  const LocationWidget({
    super.key,
    this.displayMode = LocationDisplayMode.mapAndAddress,
    this.initialLatitude,
    this.initialLongitude,
    this.mapZoom = 14.0,
    this.showMapControls = true,
    this.mapType = MapType.normal,
    this.enableMapGestures = true,
    this.markers,
    this.showUserLocation = true,
    this.initialAddress,
    this.showAddressField = true,
    this.enableAddressSearch = true,
    this.showCoordinates = false,
    this.addressHint = 'Enter address',
    this.readOnly = false,
    this.showCurrentLocationButton = true,
    this.autoDetectLocation = false,
    this.locationAccuracy = LocationAccuracy.high,
    this.width,
    this.height,
    this.backgroundColor = Colors.white,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = true,
    this.elevation = 2.0,
    this.padding = const EdgeInsets.all(8.0),
    this.margin = const EdgeInsets.all(8.0),
    this.label,
    this.labelStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.showLabel = true,
    this.currentLocationButtonText = 'Current Location',
    this.searchButtonText = 'Search',
    this.buttonColor = const Color(0xFF0058FF),
    this.buttonTextColor = Colors.white,
    this.onLocationChanged,
    this.onAddressChanged,
    this.onCurrentLocationDetected,
    this.onError,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Animation properties
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Location-specific JSON configuration
    this.useJsonLocationConfig = false,
    this.locationConfig,
  });

  /// Creates a LocationWidget from a JSON map
  ///
  /// This factory constructor allows for creating a LocationWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory LocationWidget.fromJson(Map<String, dynamic> json) {
    // Parse display mode
    LocationDisplayMode displayMode = LocationDisplayMode.mapAndAddress;
    if (json.containsKey('displayMode')) {
      final String mode = json['displayMode'].toString().toLowerCase();
      if (mode == 'maponly') {
        displayMode = LocationDisplayMode.mapOnly;
      } else if (mode == 'addressonly') {
        displayMode = LocationDisplayMode.addressOnly;
      }
    }

    // Parse map type
    MapType mapType = MapType.normal;
    if (json.containsKey('mapType')) {
      final String type = json['mapType'].toString().toLowerCase();
      if (type == 'satellite') {
        mapType = MapType.satellite;
      } else if (type == 'hybrid') {
        mapType = MapType.hybrid;
      } else if (type == 'terrain') {
        mapType = MapType.terrain;
      }
    }

    // Parse location accuracy
    LocationAccuracy locationAccuracy = LocationAccuracy.high;
    if (json.containsKey('locationAccuracy')) {
      final String accuracy = json['locationAccuracy'].toString().toLowerCase();
      if (accuracy == 'lowest') {
        locationAccuracy = LocationAccuracy.lowest;
      } else if (accuracy == 'low') {
        locationAccuracy = LocationAccuracy.low;
      } else if (accuracy == 'medium') {
        locationAccuracy = LocationAccuracy.medium;
      } else if (accuracy == 'best') {
        locationAccuracy = LocationAccuracy.best;
      } else if (accuracy == 'bestfornavigation') {
        locationAccuracy = LocationAccuracy.bestForNavigation;
      }
    }

    // Parse colors
    Color backgroundColor = Colors.white;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color borderColor = Colors.grey;
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    Color buttonColor = Colors.blue;
    if (json.containsKey('buttonColor')) {
      buttonColor = _parseColor(json['buttonColor']);
    }

    Color buttonTextColor = Colors.white;
    if (json.containsKey('buttonTextColor')) {
      buttonTextColor = _parseColor(json['buttonTextColor']);
    }

    Color? hoverColor;
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.all(8.0);
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = const EdgeInsets.all(8.0);
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    // Parse animation properties
    Duration animationDuration = const Duration(milliseconds: 300);
    if (json.containsKey('animationDuration')) {
      animationDuration = Duration(
        milliseconds: json['animationDuration'] as int? ?? 300,
      );
    }

    Curve animationCurve = Curves.easeInOut;
    if (json.containsKey('animationCurve')) {
      final String curve = json['animationCurve'].toString().toLowerCase();
      if (curve == 'linear') {
        animationCurve = Curves.linear;
      } else if (curve == 'decelerate') {
        animationCurve = Curves.decelerate;
      } else if (curve == 'ease') {
        animationCurve = Curves.ease;
      } else if (curve == 'easein') {
        animationCurve = Curves.easeIn;
      } else if (curve == 'easeout') {
        animationCurve = Curves.easeOut;
      } else if (curve == 'elasticin') {
        animationCurve = Curves.elasticIn;
      } else if (curve == 'elasticout') {
        animationCurve = Curves.elasticOut;
      } else if (curve == 'elasticinout') {
        animationCurve = Curves.elasticInOut;
      }
    }

    // Parse text style
    TextStyle? labelStyle;
    if (json.containsKey('labelStyle')) {
      final Map<String, dynamic> styleJson =
          json['labelStyle'] as Map<String, dynamic>;
      labelStyle = TextStyle(
        color:
            styleJson.containsKey('color')
                ? _parseColor(styleJson['color'])
                : null,
        fontSize:
            styleJson.containsKey('fontSize')
                ? (styleJson['fontSize'] as num).toDouble()
                : null,
        fontWeight:
            styleJson.containsKey('fontWeight')
                ? _parseFontWeight(styleJson['fontWeight'])
                : null,
        fontStyle:
            styleJson.containsKey('fontStyle')
                ? _parseFontStyle(styleJson['fontStyle'])
                : null,
      );
    }

    // Parse icons
    IconData? prefixIcon;
    if (json.containsKey('prefixIcon')) {
      prefixIcon = _parseIconData(json['prefixIcon']);
    }

    IconData? suffixIcon;
    if (json.containsKey('suffixIcon')) {
      suffixIcon = _parseIconData(json['suffixIcon']);
    }

    // Parse markers
    Set<Marker>? markers;
    if (json.containsKey('markers') && json['markers'] is List) {
      final List<dynamic> markersList = json['markers'] as List<dynamic>;
      markers =
          markersList.map((markerJson) {
            final Map<String, dynamic> marker =
                markerJson as Map<String, dynamic>;
            return Marker(
              markerId: MarkerId(marker['id'] as String? ?? 'marker'),
              position: LatLng(
                marker['latitude'] as double? ?? 0.0,
                marker['longitude'] as double? ?? 0.0,
              ),
              infoWindow:
                  marker.containsKey('title') || marker.containsKey('snippet')
                      ? InfoWindow(
                        title: marker['title'] as String?,
                        snippet: marker['snippet'] as String?,
                      )
                      : InfoWindow.noText,
            );
          }).toSet();
    }

    return LocationWidget(
      // Basic properties
      displayMode: displayMode,
      initialLatitude: json['initialLatitude'] as double?,
      initialLongitude: json['initialLongitude'] as double?,
      mapZoom: json['mapZoom'] as double? ?? 14.0,
      showMapControls: json['showMapControls'] as bool? ?? true,
      mapType: mapType,
      enableMapGestures: json['enableMapGestures'] as bool? ?? true,
      markers: markers,
      showUserLocation: json['showUserLocation'] as bool? ?? true,
      initialAddress: json['initialAddress'] as String?,
      showAddressField: json['showAddressField'] as bool? ?? true,
      enableAddressSearch: json['enableAddressSearch'] as bool? ?? true,
      showCoordinates: json['showCoordinates'] as bool? ?? false,
      addressHint: json['addressHint'] as String? ?? 'Enter address',
      readOnly: json['readOnly'] as bool? ?? false,
      showCurrentLocationButton:
          json['showCurrentLocationButton'] as bool? ?? true,
      autoDetectLocation: json['autoDetectLocation'] as bool? ?? false,
      locationAccuracy: locationAccuracy,

      // Appearance properties
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      backgroundColor: backgroundColor,
      borderRadius: json['borderRadius'] as double? ?? 8.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      borderColor: borderColor,
      borderWidth: json['borderWidth'] as double? ?? 1.0,
      hasShadow: json['hasShadow'] as bool? ?? true,
      elevation: json['elevation'] as double? ?? 2.0,
      padding: padding,
      margin: margin,

      // Label properties
      label: json['label'] as String?,
      labelStyle: labelStyle,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      showLabel: json['showLabel'] as bool? ?? true,

      // Button properties
      currentLocationButtonText:
          json['currentLocationButtonText'] as String? ?? 'Current Location',
      searchButtonText: json['searchButtonText'] as String? ?? 'Search',
      buttonColor: buttonColor,
      buttonTextColor: buttonTextColor,

      // Advanced interaction properties
      hoverColor: hoverColor,
      focusColor: focusColor,
      autofocus: json['autofocus'] as bool? ?? false,
      enableFeedback: json['enableFeedback'] as bool? ?? true,

      // Animation properties
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      animationDuration: animationDuration,
      animationCurve: animationCurve,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      callbackState:
          json.containsKey('callbackState')
              ? json['callbackState'] as Map<String, dynamic>
              : null,
      jsonConfig: json,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonLocationConfig: json['useJsonLocationConfig'] as bool? ?? false,
      locationConfig:
          json.containsKey('locationConfig')
              ? json['locationConfig'] as Map<String, dynamic>
              : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          default:
            return Color(0xFF0058FF);
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Color(0xFF0058FF); // Default color
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') ||
          value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal:
              value.containsKey('horizontal')
                  ? (value['horizontal'] as num).toDouble()
                  : 0.0,
          vertical:
              value.containsKey('vertical')
                  ? (value['vertical'] as num).toDouble()
                  : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom')
              ? (value['bottom'] as num).toDouble()
              : 0.0,
        );
      }
    }
    return const EdgeInsets.all(8.0); // Default padding
  }

  /// Parses font weight from a string or number
  static FontWeight _parseFontWeight(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'thin':
          return FontWeight.w100;
        case 'extralight':
          return FontWeight.w200;
        case 'light':
          return FontWeight.w300;
        case 'regular':
          return FontWeight.w400;
        case 'medium':
          return FontWeight.w500;
        case 'semibold':
          return FontWeight.w600;
        case 'bold':
          return FontWeight.w700;
        case 'extrabold':
          return FontWeight.w800;
        case 'black':
          return FontWeight.w900;
        default:
          return FontWeight.normal;
      }
    } else if (value is int) {
      switch (value) {
        case 100:
          return FontWeight.w100;
        case 200:
          return FontWeight.w200;
        case 300:
          return FontWeight.w300;
        case 400:
          return FontWeight.w400;
        case 500:
          return FontWeight.w500;
        case 600:
          return FontWeight.w600;
        case 700:
          return FontWeight.w700;
        case 800:
          return FontWeight.w800;
        case 900:
          return FontWeight.w900;
        default:
          return FontWeight.normal;
      }
    }
    return FontWeight.normal; // Default font weight
  }

  /// Parses font style from a string
  static FontStyle _parseFontStyle(String value) {
    switch (value.toLowerCase()) {
      case 'italic':
        return FontStyle.italic;
      default:
        return FontStyle.normal;
    }
  }

  /// Parses icon data from a string
  static IconData? _parseIconData(String value) {
    switch (value.toLowerCase()) {
      case 'location':
        return Icons.location_on;
      case 'map':
        return Icons.map;
      case 'search':
        return Icons.search;
      case 'gps':
        return Icons.gps_fixed;
      case 'place':
        return Icons.place;
      case 'navigation':
        return Icons.navigation;
      case 'directions':
        return Icons.directions;
      case 'pin':
        return Icons.pin_drop;
      case 'home':
        return Icons.home;
      case 'work':
        return Icons.work;
      case 'star':
        return Icons.star;
      case 'favorite':
        return Icons.favorite;
      case 'person':
        return Icons.person;
      case 'settings':
        return Icons.settings;
      case 'info':
        return Icons.info;
      case 'help':
        return Icons.help;
      case 'warning':
        return Icons.warning;
      case 'error':
        return Icons.error;
      case 'check':
        return Icons.check;
      case 'close':
        return Icons.close;
      default:
        return null;
    }
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'displayMode': displayMode.toString().split('.').last,
      'initialLatitude': initialLatitude,
      'initialLongitude': initialLongitude,
      'mapZoom': mapZoom,
      'showMapControls': showMapControls,
      'mapType': mapType.toString().split('.').last,
      'enableMapGestures': enableMapGestures,
      'showUserLocation': showUserLocation,
      'initialAddress': initialAddress,
      'showAddressField': showAddressField,
      'enableAddressSearch': enableAddressSearch,
      'showCoordinates': showCoordinates,
      'addressHint': addressHint,
      'readOnly': readOnly,
      'showCurrentLocationButton': showCurrentLocationButton,
      'autoDetectLocation': autoDetectLocation,
      'locationAccuracy': locationAccuracy.toString().split('.').last,

      // Appearance properties
      'width': width,
      'height': height,
      'backgroundColor': '#${backgroundColor.toHexString()}',
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'borderColor': '#${borderColor.toHexString()}',
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,

      // Label properties
      'label': label,
      'showLabel': showLabel,

      // Button properties
      'currentLocationButtonText': currentLocationButtonText,
      'searchButtonText': searchButtonText,
      'buttonColor': '#${buttonColor.toHexString()}',
      'buttonTextColor': '#${buttonTextColor.toHexString()}',

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Animation properties
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonLocationConfig': useJsonLocationConfig,
    };
  }

  @override
  State<LocationWidget> createState() => _LocationWidgetState();
}

class _LocationWidgetState extends State<LocationWidget>
    with SingleTickerProviderStateMixin {
  // Controllers
  final TextEditingController _addressController = TextEditingController();
  GoogleMapController? _mapController;

  // Location data
  double? _latitude;
  double? _longitude;
  String? _address;
  bool _isLoading = false;
  String? _errorMessage;
  bool _isHovering = false;
  bool _isFocused = false;

  // Markers
  Set<Marker> _markers = {};

  // Animation and interaction state
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();

    // Initialize location data
    _latitude = widget.initialLatitude;
    _longitude = widget.initialLongitude;
    _address = widget.initialAddress;

    if (_address != null) {
      _addressController.text = _address!;
    }

    // Initialize markers
    if (widget.markers != null) {
      _markers = widget.markers!;
    } else if (_latitude != null && _longitude != null) {
      _markers = {
        Marker(
          markerId: const MarkerId('selected_location'),
          position: LatLng(_latitude!, _longitude!),
        ),
      };
    }

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );

    // Initialize with full opacity if not animating
    if (!widget.hasAnimation) {
      _animationController.value = 1.0;
    } else {
      _animationController.forward();
    }

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    // Auto-detect location if enabled
    if (widget.autoDetectLocation) {
      _getCurrentLocation();
    }
  }

  @override
  void didUpdateWidget(LocationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation duration and curve if changed
    if (oldWidget.animationDuration != widget.animationDuration) {
      _animationController.duration = widget.animationDuration;
    }

    if (oldWidget.animationCurve != widget.animationCurve) {
      _animation = CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      );
    }

    // Handle animation state changes
    if (!oldWidget.hasAnimation && widget.hasAnimation) {
      _animationController.forward(from: 0.0);
    } else if (oldWidget.hasAnimation && !widget.hasAnimation) {
      _animationController.value = 1.0;
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _addressController.dispose();
    _mapController?.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  // Get current location
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw 'Location permission denied';
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw 'Location permission permanently denied';
      }

      // Get current position
      final Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: widget.locationAccuracy,
      );

      // Update state
      setState(() {
        _latitude = position.latitude;
        _longitude = position.longitude;
        _markers = {
          Marker(
            markerId: const MarkerId('current_location'),
            position: LatLng(position.latitude, position.longitude),
          ),
        };
        _isLoading = false;
      });

      // Update map camera
      _mapController?.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(position.latitude, position.longitude),
          widget.mapZoom,
        ),
      );

      // Get address from coordinates
      _getAddressFromCoordinates(position.latitude, position.longitude);

      // Call callback
      if (widget.onCurrentLocationDetected != null) {
        widget.onCurrentLocationDetected!(position);
      }

      if (widget.onLocationChanged != null) {
        widget.onLocationChanged!(position.latitude, position.longitude);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });

      if (widget.onError != null) {
        widget.onError!(e.toString());
      }
    }
  }

  // Get address from coordinates
  Future<void> _getAddressFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        latitude,
        longitude,
      );
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        final address = _formatAddress(place);

        setState(() {
          _address = address;
          _addressController.text = address;
        });

        if (widget.onAddressChanged != null) {
          widget.onAddressChanged!(address);
        }
      }
    } catch (e) {
      if (widget.onError != null) {
        widget.onError!('Failed to get address: $e');
      }
    }
  }

  // Get coordinates from address
  Future<void> _getCoordinatesFromAddress(String address) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      List<Location> locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        Location location = locations.first;

        setState(() {
          _latitude = location.latitude;
          _longitude = location.longitude;
          _markers = {
            Marker(
              markerId: const MarkerId('searched_location'),
              position: LatLng(location.latitude, location.longitude),
            ),
          };
          _isLoading = false;
        });

        // Update map camera
        _mapController?.animateCamera(
          CameraUpdate.newLatLngZoom(
            LatLng(location.latitude, location.longitude),
            widget.mapZoom,
          ),
        );

        if (widget.onLocationChanged != null) {
          widget.onLocationChanged!(location.latitude, location.longitude);
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Address not found';
      });

      if (widget.onError != null) {
        widget.onError!('Failed to find location: $e');
      }
    }
  }

  // Format address from placemark
  String _formatAddress(Placemark place) {
    List<String> addressParts = [
      place.street ?? '',
      place.subLocality ?? '',
      place.locality ?? '',
      place.postalCode ?? '',
      place.administrativeArea ?? '',
      place.country ?? '',
    ];

    // Filter out empty parts
    addressParts = addressParts.where((part) => part.isNotEmpty).toList();

    return addressParts.join(', ');
  }

  // Handle map tap
  void _onMapTap(LatLng position) {
    if (widget.readOnly) return;

    setState(() {
      _latitude = position.latitude;
      _longitude = position.longitude;
      _markers = {
        Marker(markerId: const MarkerId('tapped_location'), position: position),
      };
    });

    _getAddressFromCoordinates(position.latitude, position.longitude);

    if (widget.onLocationChanged != null) {
      widget.onLocationChanged!(position.latitude, position.longitude);
    }
  }

  // Build map widget
  Widget _buildMap() {
    return SizedBox(
      width: widget.width,
      height: widget.height ?? 200,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child:
            _latitude != null && _longitude != null
                ? GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: LatLng(_latitude!, _longitude!),
                    zoom: widget.mapZoom,
                  ),
                  markers: _markers,
                  mapType: widget.mapType,
                  zoomControlsEnabled: widget.showMapControls,
                  zoomGesturesEnabled: widget.enableMapGestures,
                  scrollGesturesEnabled: widget.enableMapGestures,
                  rotateGesturesEnabled: widget.enableMapGestures,
                  tiltGesturesEnabled: widget.enableMapGestures,
                  myLocationEnabled: widget.showUserLocation,
                  myLocationButtonEnabled: widget.showUserLocation,
                  onTap: _onMapTap,
                  onMapCreated: (controller) {
                    _mapController = controller;
                  },
                )
                : const Center(child: Text('No location selected')),
      ),
    );
  }

  // Build address field
  Widget _buildAddressField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showLabel && widget.label != null) ...[
          Text(
            widget.label!,
            style:
                widget.labelStyle ??
                // TextStyle(
                //   fontSize: _getResponsiveFontSize(context),
                //   fontWeight: FontWeight.w500,
                // ),
                FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontSize: _getResponsiveFontSize(context),
                  fontWeight: FontManager.medium,
                ),
          ),
          SizedBox(height: _getResponsiveBoxsize(context)),
        ],
        MouseRegion(
          onEnter: (_) => setState(() => _isHovering = true),
          onExit: (_) => setState(() => _isHovering = false),
          child: Container(
            height: _getResponsiveHeight(context),
            decoration: BoxDecoration(
              color:
                  _isFocused && widget.focusColor != null
                      ? widget.focusColor
                      : _isHovering
                      ? (widget.hoverColor ?? Colors.white)
                      : widget.backgroundColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Focus(
              onFocusChange: (hasFocus) {
                setState(() {
                  _isFocused = hasFocus;
                });
                if (widget.onFocus != null) {
                  widget.onFocus!(hasFocus);
                }
              },
              child: TextField(
                controller: _addressController,
                //style: TextStyle(fontSize: _getResponsiveIconSize(context)),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontSize: _getResponsiveValueFontSize(context),
                  fontWeight: FontManager.medium,
                  color: Color(0xFF333333),
                ),
                decoration: InputDecoration(
                  hintText: widget.addressHint,
                  suffixIcon: _buildSuffixIcon(),
                  contentPadding: _getResponsivePadding(context),
                  fillColor:
                      _isFocused && widget.focusColor != null
                          ? widget.focusColor
                          : _isHovering
                          ? (widget.hoverColor ?? Colors.white)
                          : widget.backgroundColor,
                  filled: true,
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(
                      color:
                          _isHovering ? Color(0xFF0058FF) : Color(0xFFCCCCCC),
                      width: 1.0,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(
                      color: Color(0xFF0058FF),
                      width: 1.0,
                    ),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(
                      color: Color(0xFFCCCCCC),
                      width: 0.0,
                    ),
                  ),
                ),
                readOnly: widget.readOnly || !widget.enableAddressSearch,
                onChanged: (value) {
                  _address = value;
                  if (widget.onAddressChanged != null) {
                    widget.onAddressChanged!(value);
                  }
                },
                onSubmitted:
                    widget.enableAddressSearch
                        ? (value) {
                          if (value.isNotEmpty) {
                            _getCoordinatesFromAddress(value);
                          }
                        }
                        : null,
              ),
            ),
          ),
        ),

        if (_errorMessage != null) ...[
          const SizedBox(height: 2),
          Text(
            _errorMessage!,
            //style: const TextStyle(fontSize: 10, color: Colors.red),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontSize: _getResponsiveValueFontSize(context),
              fontWeight: FontManager.medium,
              color: Colors.red,
            ),
          ),
        ],
        if (widget.showCoordinates &&
            _latitude != null &&
            _longitude != null) ...[
          // const SizedBox(height: 8),
          // Text(
          //   'Lat: ${_latitude!.toStringAsFixed(6)}, Lng: ${_longitude!.toStringAsFixed(6)}',
          //   style: const TextStyle(fontSize: 12, color: Colors.grey),
          // ),
        ],
      ],
    );
  }

  // Build suffix icon with location icon only
  Widget? _buildSuffixIcon() {
    // Return only the location icon
    return Padding(
      padding: EdgeInsets.only(left: 8, right: 8),
      child: SvgPicture.asset(
        _isHovering
            ? 'assets/images/location-hover.svg'
            : 'assets/images/location.svg',
        package: 'ui_controls_library',
        // width: _getResponsiveImageSize(context),
        // height: _getResponsiveImageSize(context),
        //color: _isHovering ? Color(0xFF0058FF) : Color(0xFF999999),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Build the widget based on display mode
    Widget content;

    switch (widget.displayMode) {
      case LocationDisplayMode.mapOnly:
        content = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showLabel && widget.label != null) ...[
              Text(
                widget.label!,
                style:
                    widget.labelStyle ??
                    //const TextStyle(fontWeight: FontWeight.bold),
                    FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontSize: _getResponsiveValueFontSize(context),
                      fontWeight: FontManager.bold,
                    ),
              ),

              const SizedBox(height: 8),
            ],
            _buildMap(),
          ],
        );
        break;

      case LocationDisplayMode.addressOnly:
        content = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [_buildAddressField()],
        );
        break;

      case LocationDisplayMode.mapAndAddress:
        content = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showLabel && widget.label != null) ...[
              Text(
                widget.label!,
                style:
                    widget.labelStyle ??
                    //const TextStyle(fontWeight: FontWeight.bold),
                    FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontSize: _getResponsiveValueFontSize(context),
                      fontWeight: FontManager.bold,
                    ),
              ),
              const SizedBox(height: 8),
            ],
            _buildMap(),
            const SizedBox(height: 16),
            _buildAddressField(),
          ],
        );
        break;
    }

    // Apply container styling
    Widget containerWidget = Container(
      width: widget.width,

      //height: _getResponsiveHeight(context),
      padding: widget.padding,
      margin: widget.margin,
      decoration: BoxDecoration(
        //color: widget.backgroundColor,
        //borderRadius: BorderRadius.circular(widget.borderRadius),
        //border:
        //widget.hasBorder
        // ? Border.all(
        //color: Color(0xFFCCCCCC),
        //width: widget.borderWidth,
        //)
        //: null,
        // boxShadow:
        //     widget.hasShadow
        //         ? [
        //           BoxShadow(
        //             color: Colors.black.withAlpha(26), // 0.1 opacity
        //             blurRadius: widget.elevation,
        //             offset: Offset(0, widget.elevation / 2),
        //           ),
        //         ]
        //         : null,
      ),
      child: content,
    );

    // Apply animation if needed
    if (widget.hasAnimation) {
      containerWidget = FadeTransition(
        opacity: _animation,
        child: containerWidget,
      );
    }

    // Apply advanced interaction properties
    if (widget.onHover != null ||
        widget.onFocus != null ||
        widget.onDoubleTap != null ||
        widget.onLongPress != null) {
      containerWidget = MouseRegion(
        onEnter: (event) {
          if (widget.onHover != null) {
            widget.onHover!(true);
          }
        },
        onExit: (event) {
          if (widget.onHover != null) {
            widget.onHover!(false);
          }
        },
        cursor:
            (widget.onDoubleTap != null || widget.onLongPress != null)
                ? SystemMouseCursors.click
                : MouseCursor.defer,
        child: GestureDetector(
          onDoubleTap:
              widget.onDoubleTap != null
                  ? () {
                    // Execute onDoubleTap callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                      _executeJsonCallback('onDoubleTap');
                    }

                    // Call standard callback
                    widget.onDoubleTap!();
                  }
                  : null,
          onLongPress:
              widget.onLongPress != null
                  ? () {
                    // Execute onLongPress callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onLongPress')) {
                      _executeJsonCallback('onLongPress');
                    }

                    // Call standard callback
                    widget.onLongPress!();
                  }
                  : null,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: widget.onFocus,
            child: containerWidget,
          ),
        ),
      );
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      containerWidget = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: containerWidget,
      );
    }

    return containerWidget;
  }

  double _getResponsiveBoxsize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 8.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 8.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 6.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 4.0; // Small (768-1024px)
    } else {
      return 4.0; // Default for very small screens
    }
  }
}

/// Enum defining the display modes for the location widget
enum LocationDisplayMode {
  /// Shows both map and address field
  mapAndAddress,

  /// Shows only the map
  mapOnly,

  /// Shows only the address field
  addressOnly,
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 16.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 14.0; // Large
  } else if (screenWidth >= 1280) {
    return 12.0; // Medium
  } else {
    return 12.0; // Default for very small screens
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px)
  } else {
    return 40.0; // Default for very small screens
  }
}

double _getResponsiveIconSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else if (screenWidth >= 768) {
    return 12.0; // Small
  } else {
    return 10.0; // Extra Small (fallback for very small screens)
  }
}

double _getResponsiveImageSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 24.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 24.0; // Large
  } else if (screenWidth >= 1280) {
    return 20.0; // Medium
  } else if (screenWidth >= 768) {
    return 16.0; // Small
  } else {
    return 16.0; // Extra Small (fallback for very small screens)
  }
}

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(
      horizontal: 12.0,
      vertical: 3.0,
    ); // Large// Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 2.0,
    ); // Medium// Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 1.0,
    ); // Default for very small screens
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}
